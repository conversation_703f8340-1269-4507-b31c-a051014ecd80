<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:gravity="center"
        android:orientation="vertical">

        <!-- 连接状态显示 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <View
                android:id="@+id/connection_indicator"
                android:layout_width="12dp"
                android:layout_height="12dp"
                android:layout_marginEnd="8dp"
                android:background="@drawable/connection_indicator_bg" />

            <TextView
                android:id="@+id/tv_connection_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="服务端连接状态: 未连接"
                android:textColor="#333333"
                android:textSize="14sp" />



        <!-- 发送日志按钮 -->
        <Button
            android:id="@+id/btn_send_logs"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            android:text="发送当前日志"
            android:textSize="14sp"
            android:padding="12dp"
            android:background="@drawable/button_background"
            android:textColor="#FFFFFF" />

        </LinearLayout>

        <!-- 输入框和发送按钮 -->
        <LinearLayout
            android:layout_width="300dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            android:orientation="horizontal">

            <EditText
                android:id="@+id/et_command_input"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:hint="输入指令内容..."
                android:background="@drawable/bg_input"
                android:padding="12dp"
                android:textSize="14sp"
                android:maxLines="1" />

            <Button
                android:id="@+id/btn_send_command"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:backgroundTint="@color/colorAccent"
                android:text="发送"
                android:textSize="12sp" />

        </LinearLayout>

        <Button
            android:id="@+id/btn_enable"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:backgroundTint="@color/colorAccent"
            android:text="开启服务" />

        <LinearLayout
            android:id="@+id/ll_option"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <Button
                android:id="@+id/btn_basic"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:backgroundTint="@color/colorAccent"
                android:text="基础" />

            <Button
                android:id="@+id/btn_pro"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:backgroundTint="@color/colorAccent"
                android:text="进阶" />

            <Button
                android:id="@+id/btn_advanced"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:backgroundTint="@color/colorAccent"
                android:text="高级" />

            <Button
                android:id="@+id/btn_web"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:backgroundTint="@color/colorAccent"
                android:text="Web支持"
                android:textAllCaps="false" />

            <Button
                android:id="@+id/btn_network_test"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:backgroundTint="@color/colorAccent"
                android:text="网络测试"
                android:textAllCaps="false" />

        </LinearLayout>

    </LinearLayout>

</FrameLayout>