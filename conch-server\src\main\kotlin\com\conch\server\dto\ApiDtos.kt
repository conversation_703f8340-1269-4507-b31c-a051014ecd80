package com.conch.server.dto

import com.fasterxml.jackson.annotation.JsonProperty
import java.time.LocalDateTime

/**
 * 指令请求DTO
 */
data class CommandRequestDto(
    @JsonProperty("textCommand")
    val textCommand: TextCommandDto,
    
    @JsonProperty("deviceInfo")
    val deviceInfo: DeviceInfoDto
)

/**
 * 文本指令DTO
 */
data class TextCommandDto(
    @JsonProperty("text")
    val text: String,
    
    @JsonProperty("confidence")
    val confidence: Double = 1.0,
    
    @JsonProperty("timestamp")
    val timestamp: Long = System.currentTimeMillis()
)

/**
 * 设备信息DTO
 */
data class DeviceInfoDto(
    @JsonProperty("model")
    val model: String,
    
    @JsonProperty("androidVersion")
    val androidVersion: String,
    
    @JsonProperty("screenResolution")
    val screenResolution: String,
    
    @JsonProperty("installedApps")
    val installedApps: List<String> = emptyList()
)

/**
 * 指令响应DTO
 */
data class CommandResponseDto(
    @JsonProperty("sessionId")
    val sessionId: String,
    
    @JsonProperty("state")
    val state: String,
    
    @JsonProperty("message")
    val message: String,
    
    @JsonProperty("estimatedDuration")
    val estimatedDuration: Long = 0
)

/**
 * 健康检查响应DTO
 */
data class HealthCheckResponseDto(
    @JsonProperty("status")
    val status: String,
    
    @JsonProperty("timestamp")
    val timestamp: LocalDateTime,
    
    @JsonProperty("server")
    val server: ServerInfoDto,
    
    @JsonProperty("config")
    val config: ConfigInfoDto
)

/**
 * 服务器信息DTO
 */
data class ServerInfoDto(
    @JsonProperty("name")
    val name: String,
    
    @JsonProperty("version")
    val version: String,
    
    @JsonProperty("description")
    val description: String
)

/**
 * 配置信息DTO
 */
data class ConfigInfoDto(
    @JsonProperty("sessionTimeout")
    val sessionTimeout: Long,
    
    @JsonProperty("maxConcurrentSessions")
    val maxConcurrentSessions: Int,
    
    @JsonProperty("maxScriptActions")
    val maxScriptActions: Int,
    
    @JsonProperty("defaultActionTimeout")
    val defaultActionTimeout: Long
)

/**
 * 执行脚本DTO
 */
data class ExecutionScriptDto(
    @JsonProperty("sessionId")
    val sessionId: String,
    
    @JsonProperty("scriptId")
    val scriptId: String,
    
    @JsonProperty("actions")
    val actions: List<ActionDto>,
    
    @JsonProperty("metadata")
    val metadata: ScriptMetadataDto
)

/**
 * 动作DTO
 */
data class ActionDto(
    @JsonProperty("type")
    val type: String,
    
    @JsonProperty("target")
    val target: ActionTargetDto,
    
    @JsonProperty("parameters")
    val parameters: Map<String, Any> = emptyMap(),
    
    @JsonProperty("timeout")
    val timeout: Long = 5000
)

/**
 * 动作目标DTO
 */
data class ActionTargetDto(
    @JsonProperty("type")
    val type: String,
    
    @JsonProperty("value")
    val value: String,
    
    @JsonProperty("x")
    val x: Int = 0,
    
    @JsonProperty("y")
    val y: Int = 0
)

/**
 * 脚本元数据DTO
 */
data class ScriptMetadataDto(
    @JsonProperty("estimatedDuration")
    val estimatedDuration: Long,
    
    @JsonProperty("retryCount")
    val retryCount: Int,
    
    @JsonProperty("priority")
    val priority: String
)

/**
 * 反馈请求DTO
 */
data class FeedbackRequestDto(
    @JsonProperty("sessionId")
    val sessionId: String,
    
    @JsonProperty("scriptId")
    val scriptId: String,
    
    @JsonProperty("executionResult")
    val executionResult: ExecutionResultDto,
    
    @JsonProperty("screenshot")
    val screenshot: String? = null,
    
    @JsonProperty("deviceState")
    val deviceState: DeviceStateDto? = null,
    
    @JsonProperty("timestamp")
    val timestamp: Long = System.currentTimeMillis()
)

/**
 * 执行结果DTO
 */
data class ExecutionResultDto(
    @JsonProperty("status")
    val status: String,
    
    @JsonProperty("completedActions")
    val completedActions: Int,
    
    @JsonProperty("totalActions")
    val totalActions: Int,
    
    @JsonProperty("executionTime")
    val executionTime: Long,
    
    @JsonProperty("errors")
    val errors: List<String> = emptyList()
)

/**
 * 设备状态DTO
 */
data class DeviceStateDto(
    @JsonProperty("currentApp")
    val currentApp: String,
    
    @JsonProperty("screenOn")
    val screenOn: Boolean,
    
    @JsonProperty("networkConnected")
    val networkConnected: Boolean,
    
    @JsonProperty("batteryLevel")
    val batteryLevel: Int
)

/**
 * 通用响应DTO
 */
data class ApiResponseDto<T>(
    @JsonProperty("success")
    val success: Boolean,

    @JsonProperty("data")
    val data: T? = null,

    @JsonProperty("message")
    val message: String? = null,

    @JsonProperty("timestamp")
    val timestamp: LocalDateTime = LocalDateTime.now()
)

/**
 * 日志上传请求DTO
 */
data class LogUploadRequestDto(
    @JsonProperty("deviceInfo")
    val deviceInfo: DeviceInfoDto,

    @JsonProperty("logContent")
    val logContent: String,

    @JsonProperty("logLevel")
    val logLevel: String = "INFO",

    @JsonProperty("timestamp")
    val timestamp: Long = System.currentTimeMillis(),

    @JsonProperty("sessionId")
    val sessionId: String? = null
)

/**
 * 日志上传响应DTO
 */
data class LogUploadResponseDto(
    @JsonProperty("success")
    val success: Boolean,

    @JsonProperty("message")
    val message: String,

    @JsonProperty("logId")
    val logId: String? = null,

    @JsonProperty("timestamp")
    val timestamp: Long = System.currentTimeMillis()
)
