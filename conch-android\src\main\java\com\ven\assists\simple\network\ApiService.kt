package com.ven.assists.simple.network

import retrofit2.Response
import retrofit2.http.*

/**
 * REST API接口定义
 */
interface ApiService {

    /**
     * 发送指令信息到服务端
     */
    @POST("api/v1/voice/command")
    suspend fun sendCommand(@Body command: CommandRequestDto): Response<CommandResponseDto>

    /**
     * 获取服务端状态
     */
    @GET("api/v1/health")
    suspend fun getServerStatus(): Response<HealthCheckResponseDto>

    /**
     * 心跳检测
     */
    @POST("api/v1/heartbeat")
    suspend fun heartbeat(@Body heartbeat: HeartbeatRequest): Response<HeartbeatResponse>
}

/**
 * 指令请求数据类 - 匹配服务端CommandRequestDto
 */
data class CommandRequestDto(
    val textCommand: TextCommandDto,
    val deviceInfo: DeviceInfoDto,
)

/**
 * 文本指令数据类
 */
data class TextCommandDto(
    val text: String,
    val confidence: Double = 1.0,
    val timestamp: Long = System.currentTimeMillis(),
)

/**
 * 设备信息数据类
 */
data class DeviceInfoDto(
    val model: String,
    val androidVersion: String,
    val screenResolution: String,
    val installedApps: List<String> = emptyList(),
)

/**
 * 指令响应数据类 - 匹配服务端CommandResponseDto
 */
data class CommandResponseDto(
    val sessionId: String,
    val state: String,
    val message: String,
    val estimatedDuration: Long = 0,
)

/**
 * 健康检查响应数据类
 */
data class HealthCheckResponseDto(
    val status: String,
    val timestamp: String,
    val server: ServerInfoDto,
    val config: ConfigInfoDto,
)

/**
 * 服务器信息数据类
 */
data class ServerInfoDto(
    val name: String,
    val version: String,
    val description: String,
)

/**
 * 配置信息数据类
 */
data class ConfigInfoDto(
    val sessionTimeout: Long,
    val maxConcurrentSessions: Int,
    val maxScriptActions: Int,
    val defaultActionTimeout: Long,
)

/**
 * 心跳请求数据类
 */
data class HeartbeatRequest(
    val deviceId: String,
    val timestamp: Long,
)

/**
 * 心跳响应数据类
 */
data class HeartbeatResponse(
    val success: Boolean,
    val serverTime: Long,
)
