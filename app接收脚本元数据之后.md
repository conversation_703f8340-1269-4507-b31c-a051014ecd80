# App接收脚本元数据之后的处理方案

## 1. 服务端脚本元数据格式

### 1.1 完整数据结构
```json
{
  "scriptId": "wechat_contacts_scroll_001",
  "scriptName": "微信通讯录滚动脚本",
  "version": "1.0.0",
  "description": "启动微信，点击通讯录，然后下滑",
  "targetApp": {
    "packageName": "com.tencent.mm",
    "activityName": "com.tencent.mm.ui.LauncherUI"
  },
  "steps": [
    {
      "stepTag": 1,
      "stepName": "启动微信",
      "actions": [
        {
          "type": "LAUNCH_APP",
          "params": {
            "packageName": "com.tencent.mm",
            "activityName": "com.tencent.mm.ui.LauncherUI"
          },
          "nextStep": 2,
          "timeout": 5000
        }
      ],
      "retryConfig": {
        "maxRetries": 3,
        "retryDelay": 1000
      }
    },
    {
      "stepTag": 2,
      "stepName": "验证微信主页并点击通讯录",
      "actions": [
        {
          "type": "FIND_AND_CLICK",
          "params": {
            "findMethod": "findByText",
            "targetText": "通讯录",
            "positionCheck": {
              "minX": 340,
              "minY": 1850,
              "baseWidth": 1080,
              "baseHeight": 1920
            }
          },
          "nextStep": 3,
          "timeout": 10000
        }
      ],
      "errorHandling": [
        {
          "condition": "WRONG_PACKAGE",
          "action": "BACK_AND_RETRY",
          "targetPackage": "com.tencent.mm"
        },
        {
          "condition": "MAX_RETRIES_REACHED",
          "action": "RESTART_FROM_STEP",
          "targetStep": 1
        }
      ],
      "retryConfig": {
        "maxRetries": 5,
        "retryDelay": 2000
      }
    },
    {
      "stepTag": 3,
      "stepName": "滚动通讯录列表",
      "actions": [
        {
          "type": "SCROLL_LIST",
          "params": {
            "findMethod": "findByTags",
            "targetClass": "androidx.recyclerview.widget.RecyclerView",
            "scrollDirection": "FORWARD",
            "scrollCount": 5,
            "scrollDelay": 500
          },
          "nextStep": -1,
          "timeout": 30000
        }
      ],
      "retryConfig": {
        "maxRetries": 3,
        "retryDelay": 1000
      }
    }
  ],
  "globalConfig": {
    "logLevel": "INFO",
    "enableDebug": true,
    "defaultTimeout": 10000
  }
}
```

### 1.2 动作类型定义
```json
{
  "actionTypes": {
    "LAUNCH_APP": "启动应用",
    "FIND_AND_CLICK": "查找并点击元素",
    "INPUT_TEXT": "输入文本",
    "SCROLL_LIST": "滚动列表",
    "GESTURE_SWIPE": "手势滑动",
    "WAIT": "等待",
    "BACK": "返回",
    "VERIFY_PAGE": "验证页面"
  },
  "findMethods": {
    "findByText": "根据文本查找",
    "findById": "根据ID查找",
    "findByTags": "根据类名查找"
  },
  "errorConditions": {
    "WRONG_PACKAGE": "包名不匹配",
    "ELEMENT_NOT_FOUND": "元素未找到",
    "MAX_RETRIES_REACHED": "达到最大重试次数",
    "TIMEOUT": "超时"
  }
}
```

## 2. 客户端处理流程

### 2.1 流程图
```mermaid
graph TD
    A[接收服务端元数据] --> B[解析JSON数据]
    B --> C[验证数据格式]
    C --> D{验证通过?}
    D -->|否| E[记录错误日志]
    D -->|是| F[创建DynamicStepImpl实例]
    F --> G[注册到StepManager]
    G --> H[开始执行第一步]
    H --> I[解析当前步骤动作]
    I --> J[执行动作]
    J --> K{执行成功?}
    K -->|是| L[跳转到下一步]
    K -->|否| M[检查重试配置]
    M --> N{可以重试?}
    N -->|是| O[等待重试延迟]
    O --> J
    N -->|否| P[执行错误处理]
    P --> Q{错误处理类型}
    Q -->|BACK_AND_RETRY| R[返回并重试]
    Q -->|RESTART_FROM_STEP| S[从指定步骤重新开始]
    Q -->|STOP| T[停止执行]
    L --> U{是否为最后一步?}
    U -->|否| I
    U -->|是| V[执行完成]
    R --> J
    S --> H
```

### 2.2 核心处理类

#### ScriptMetadataParser - 元数据解析器
```kotlin
class ScriptMetadataParser {

    data class ScriptMetadata(
        val scriptId: String,
        val scriptName: String,
        val version: String,
        val description: String,
        val targetApp: AppInfo,
        val steps: List<StepMetadata>,
        val globalConfig: GlobalConfig
    )

    data class StepMetadata(
        val stepTag: Int,
        val stepName: String,
        val actions: List<ActionMetadata>,
        val errorHandling: List<ErrorHandling>?,
        val retryConfig: RetryConfig
    )

    data class ActionMetadata(
        val type: String,
        val params: Map<String, Any>,
        val nextStep: Int,
        val timeout: Long
    )

    fun parseFromJson(jsonString: String): ScriptMetadata? {
        return try {
            Gson().fromJson(jsonString, ScriptMetadata::class.java)
        } catch (e: Exception) {
            LogWrapper.logAppend("解析脚本元数据失败: ${e.message}")
            null
        }
    }
}
```

#### DynamicStepImpl - 动态步骤执行器
```kotlin
class DynamicStepImpl(
    private val metadata: ScriptMetadataParser.ScriptMetadata
) : StepImpl() {

    override fun onImpl(collector: StepCollector) {
        var currentCollector = collector

        metadata.steps.forEach { stepMetadata ->
            currentCollector = currentCollector.next(stepMetadata.stepTag) { stepContext ->
                LogWrapper.logAppend("执行步骤: ${stepMetadata.stepName}")
                executeStepActions(stepMetadata, stepContext)
            }
        }
    }

    private fun executeStepActions(
        stepMetadata: ScriptMetadataParser.StepMetadata,
        context: StepContext
    ): Step {
        stepMetadata.actions.forEach { action ->
            val result = ActionExecutor.execute(action)

            return when (result.status) {
                ActionResult.SUCCESS -> {
                    if (action.nextStep == -1) Step.none
                    else Step.get(action.nextStep)
                }
                ActionResult.FAILED -> {
                    handleActionFailure(stepMetadata, context, result.error)
                }
                ActionResult.RETRY -> Step.repeat
            }
        }

        return Step.none
    }

    private fun handleActionFailure(
        stepMetadata: ScriptMetadataParser.StepMetadata,
        context: StepContext,
        error: String
    ): Step {
        // 检查错误处理配置
        stepMetadata.errorHandling?.forEach { errorHandler ->
            if (shouldHandleError(errorHandler, error, context)) {
                return executeErrorAction(errorHandler)
            }
        }

        // 默认重试逻辑
        return if (context.repeatCount < stepMetadata.retryConfig.maxRetries) {
            Step.repeat
        } else {
            LogWrapper.logAppend("步骤执行失败，已达到最大重试次数")
            Step.none
        }
    }
}
```

#### ActionExecutor - 动作执行器
```kotlin
object ActionExecutor {

    fun execute(action: ScriptMetadataParser.ActionMetadata): ActionResult {
        return when (action.type) {
            "LAUNCH_APP" -> executeLaunchApp(action.params)
            "FIND_AND_CLICK" -> executeFindAndClick(action.params)
            "INPUT_TEXT" -> executeInputText(action.params)
            "SCROLL_LIST" -> executeScrollList(action.params)
            "GESTURE_SWIPE" -> executeGestureSwipe(action.params)
            "WAIT" -> executeWait(action.params)
            "BACK" -> executeBack()
            else -> ActionResult.failed("未知动作类型: ${action.type}")
        }
    }

    private fun executeLaunchApp(params: Map<String, Any>): ActionResult {
        return try {
            val packageName = params["packageName"] as String
            val activityName = params["activityName"] as String

            Intent().apply {
                addCategory(Intent.CATEGORY_LAUNCHER)
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                component = ComponentName(packageName, activityName)
                AssistsService.instance?.startActivity(this)
            }

            ActionResult.success()
        } catch (e: Exception) {
            ActionResult.failed("启动应用失败: ${e.message}")
        }
    }

    private fun executeFindAndClick(params: Map<String, Any>): ActionResult {
        val findMethod = params["findMethod"] as String
        val targetText = params["targetText"] as? String
        val targetId = params["targetId"] as? String
        val targetClass = params["targetClass"] as? String
        val positionCheck = params["positionCheck"] as? Map<String, Any>

        val elements = when (findMethod) {
            "findByText" -> AssistsCore.findByText(targetText ?: "")
            "findById" -> AssistsCore.findById(targetId ?: "")
            "findByTags" -> AssistsCore.findByTags(targetClass ?: "")
            else -> emptyList()
        }

        elements.forEach { element ->
            if (positionCheck != null && !checkPosition(element, positionCheck)) {
                return@forEach
            }

            val clickableElement = element.findFirstParentClickable() ?: element
            if (clickableElement.click()) {
                LogWrapper.logAppend("成功点击元素: $targetText")
                return ActionResult.success()
            }
        }

        return ActionResult.failed("未找到可点击的元素")
    }

    private fun executeScrollList(params: Map<String, Any>): ActionResult {
        val findMethod = params["findMethod"] as String
        val targetClass = params["targetClass"] as String
        val scrollDirection = params["scrollDirection"] as String
        val scrollCount = (params["scrollCount"] as Double).toInt()
        val scrollDelay = (params["scrollDelay"] as Double).toLong()

        val elements = when (findMethod) {
            "findByTags" -> AssistsCore.findByTags(targetClass)
            else -> emptyList()
        }

        elements.forEach { element ->
            repeat(scrollCount) {
                val success = when (scrollDirection) {
                    "FORWARD" -> element.scrollForward()
                    "BACKWARD" -> element.scrollBackward()
                    else -> false
                }

                if (!success) {
                    LogWrapper.logAppend("滚动到底部，停止滚动")
                    return ActionResult.success()
                }

                Thread.sleep(scrollDelay)
            }
            return ActionResult.success()
        }

        return ActionResult.failed("未找到可滚动的列表")
    }

    private fun checkPosition(
        element: AccessibilityNodeInfo,
        positionCheck: Map<String, Any>
    ): Boolean {
        val screen = element.getBoundsInScreen()
        val minX = (positionCheck["minX"] as Double).toInt()
        val minY = (positionCheck["minY"] as Double).toInt()
        val baseWidth = (positionCheck["baseWidth"] as Double).toInt()
        val baseHeight = (positionCheck["baseHeight"] as Double).toInt()

        return screen.left > AssistsCore.getX(baseWidth, minX) &&
               screen.top > AssistsCore.getY(baseHeight, minY)
    }
}

data class ActionResult(
    val status: Status,
    val error: String? = null
) {
    enum class Status { SUCCESS, FAILED, RETRY }

    companion object {
        fun success() = ActionResult(Status.SUCCESS)
        fun failed(error: String) = ActionResult(Status.FAILED, error)
        fun retry() = ActionResult(Status.RETRY)
    }
}
```

#### ScriptManager - 脚本管理器
```kotlin
class ScriptManager {

    suspend fun receiveAndExecuteScript(jsonMetadata: String) {
        val metadata = ScriptMetadataParser().parseFromJson(jsonMetadata)

        if (metadata == null) {
            LogWrapper.logAppend("脚本元数据解析失败")
            return
        }

        LogWrapper.logAppend("开始执行脚本: ${metadata.scriptName}")

        val dynamicScript = DynamicStepImpl(metadata)
        StepManager.execute(dynamicScript::class.java, StepTag.STEP_1)
    }

    fun validateMetadata(metadata: ScriptMetadataParser.ScriptMetadata): Boolean {
        // 验证必要字段
        if (metadata.scriptId.isEmpty() || metadata.steps.isEmpty()) {
            return false
        }

        // 验证步骤连续性
        val stepTags = metadata.steps.map { it.stepTag }.sorted()
        for (i in stepTags.indices) {
            if (stepTags[i] != i + 1) {
                LogWrapper.logAppend("步骤标签不连续: ${stepTags[i]}")
                return false
            }
        }

        return true
    }
}
```

## 3. 使用示例

### 3.1 在Activity中使用
```kotlin
class MainActivity : AppCompatActivity() {

    private val scriptManager = ScriptManager()

    fun onReceiveScriptFromServer(jsonMetadata: String) {
        lifecycleScope.launch {
            scriptManager.receiveAndExecuteScript(jsonMetadata)
        }
    }
}
```

### 3.2 完整的执行流程
1. **接收元数据**: 从服务端接收JSON格式的脚本元数据
2. **解析验证**: 使用ScriptMetadataParser解析并验证数据格式
3. **创建执行器**: 创建DynamicStepImpl实例
4. **注册执行**: 通过StepManager注册并开始执行
5. **动作执行**: ActionExecutor根据元数据执行具体动作
6. **错误处理**: 根据配置进行重试或错误恢复
7. **完成反馈**: 记录执行结果和日志

## 4. 优势特点

- **完全复用现有框架**: 基于StepImpl和StepManager
- **灵活的动作系统**: 支持多种常见操作类型
- **强大的错误处理**: 支持重试、回退、重新开始等策略
- **位置适配**: 支持不同分辨率的坐标转换
- **详细日志**: 完整的执行过程记录
- **易于扩展**: 可以轻松添加新的动作类型

## 5. 架构图

### 5.1 整体架构
```mermaid
graph TB
    subgraph "服务端"
        A[AI生成脚本] --> B[转换为元数据JSON]
        B --> C[发送到客户端]
    end

    subgraph "客户端 - conch-android"
        C --> D[ScriptManager接收]
        D --> E[ScriptMetadataParser解析]
        E --> F[DynamicStepImpl创建]
        F --> G[StepManager执行]

        subgraph "执行层"
            G --> H[ActionExecutor]
            H --> I[AssistsCore工具]
            I --> J[AccessibilityService]
        end

        subgraph "现有框架"
            K[StepImpl基类]
            L[StepCollector]
            M[Step状态管理]
            N[LogWrapper日志]
        end

        F -.继承.-> K
        G -.使用.-> L
        G -.使用.-> M
        H -.使用.-> N
    end

    subgraph "目标应用"
        J --> O[微信/其他App]
    end
```

### 5.2 数据流转图
```mermaid
sequenceDiagram
    participant S as 服务端
    participant SM as ScriptManager
    participant SP as ScriptMetadataParser
    participant DS as DynamicStepImpl
    participant AE as ActionExecutor
    participant AC as AssistsCore
    participant App as 目标应用

    S->>SM: 发送JSON元数据
    SM->>SP: 解析元数据
    SP->>SM: 返回ScriptMetadata对象
    SM->>DS: 创建动态步骤实例
    DS->>DS: 注册到StepManager

    loop 执行每个步骤
        DS->>AE: 执行动作
        AE->>AC: 调用工具方法
        AC->>App: 执行UI操作
        App->>AC: 返回操作结果
        AC->>AE: 返回执行状态
        AE->>DS: 返回ActionResult

        alt 成功
            DS->>DS: 跳转下一步
        else 失败
            DS->>DS: 重试或错误处理
        end
    end

    DS->>SM: 执行完成
```

这个方案完全基于现有的conch-android框架，通过动态解析服务端元数据来执行脚本，既保持了灵活性又确保了与现有代码的兼容性。